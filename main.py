import json
import asyncio
import discord
from discord.ext import commands, tasks
import socket
import struct
import time

class RconClient:
    def __init__(self, ip, port, password):
        self.ip = ip
        self.port = port
        self.password = password
        self.sock = None
        self.authenticated = False
        self.request_id = 0

    async def connect(self):
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.connect((self.ip, self.port))
            self.sock.setblocking(False)
            await self.authenticate()
            return True
        except Exception as e:
            print(f"RCON connection error: {e}")
            return False

    async def authenticate(self):
        if await self.send_command(self.password, 3):
            self.authenticated = True
            return True
        return False

    async def send_command(self, command, command_type=2):
        if not self.sock:
            if not await self.connect():
                return None

        self.request_id += 1
        packet = struct.pack('<iii', 10 + len(command), self.request_id, command_type) + command.encode('utf-8') + b'\x00\x00'
        
        loop = asyncio.get_event_loop()
        try:
            await loop.sock_sendall(self.sock, packet)
            response = await self.receive_response()
            return response
        except Exception as e:
            print(f"RCON send error: {e}")
            self.sock = None
            return None

    async def receive_response(self):
        loop = asyncio.get_event_loop()
        try:
            header = await loop.sock_recv(self.sock, 12)
            if not header:
                return None
            
            packet_size, request_id, response_type = struct.unpack('<iii', header)
            response_data = await loop.sock_recv(self.sock, packet_size - 8)
            response_text = response_data[:-2].decode('utf-8')
            
            return response_text
        except Exception as e:
            print(f"RCON receive error: {e}")
            self.sock = None
            return None

    async def get_player_count(self):
        response = await self.send_command("players")
        if response:
            try:
                # This is a simplified parsing - adjust based on actual server response format
                lines = response.strip().split('\n')
                if len(lines) > 1:
                    return len(lines) - 1  # Subtract header line
                return 0
            except:
                return 0
        return 0

    def close(self):
        if self.sock:
            self.sock.close()
            self.sock = None

class ServerBot:
    def __init__(self, server_config, refresh_rate):
        self.config = server_config
        self.refresh_rate = refresh_rate
        self.rcon = RconClient(server_config["ip"], server_config["rcon_port"], server_config["rcon_password"])
        
        intents = discord.Intents.default()
        intents.message_content = True
        self.bot = commands.Bot(command_prefix="!", intents=intents)
        
        @self.bot.event
        async def on_ready():
            print(f"Server bot {self.config['name']} is connected to Discord!")
            self.update_status.start()
        
        @tasks.loop(seconds=refresh_rate)
        async def update_status():
            try:
                player_count = await self.rcon.get_player_count()
                activity = discord.Game(f"{self.config['name']}: {player_count} players")
                await self.bot.change_presence(activity=activity)
            except Exception as e:
                print(f"Error updating status for {self.config['name']}: {e}")
        
        self.update_status = update_status
    
    async def start(self):
        try:
            await self.bot.start(self.config["discord_bot_token"])
        except Exception as e:
            print(f"Error starting bot for {self.config['name']}: {e}")
        finally:
            self.rcon.close()

class GlobalBot:
    def __init__(self, config):
        self.config = config
        self.servers = config["Servers"]
        self.refresh_rate = config["RefreshRate"]
        self.rcon_clients = {}
        
        for server in self.servers:
            self.rcon_clients[server["name"]] = RconClient(
                server["ip"], server["rcon_port"], server["rcon_password"]
            )
        
        intents = discord.Intents.default()
        intents.message_content = True
        self.bot = commands.Bot(command_prefix="!", intents=intents)
        
        @self.bot.event
        async def on_ready():
            print("Global bot is connected to Discord!")
            self.update_status.start()
        
        @self.bot.command(name="servers")
        async def servers_command(ctx):
            status_message = "**Server Status**\n"
            for server in self.servers:
                player_count = await self.rcon_clients[server["name"]].get_player_count()
                status_message += f"• {server['name']}: {player_count} players\n"
            await ctx.send(status_message)
        
        @tasks.loop(seconds=self.refresh_rate)
        async def update_status():
            try:
                total_players = 0
                for server_name, rcon in self.rcon_clients.items():
                    player_count = await rcon.get_player_count()
                    total_players += player_count
                
                activity = discord.Game(f"Monitoring {len(self.servers)} servers | {total_players} players")
                await self.bot.change_presence(activity=activity)
            except Exception as e:
                print(f"Error updating global status: {e}")
        
        self.update_status = update_status
    
    async def start(self):
        try:
            await self.bot.start(self.config["GlobalBotDiscordToken"])
        except Exception as e:
            print(f"Error starting global bot: {e}")
        finally:
            for rcon in self.rcon_clients.values():
                rcon.close()

async def main():
    try:
        with open("config.json", "r") as f:
            config = json.load(f)
        
        # Start server-specific bots
        for server in config["Servers"]:
            if server["discord_bot_token"]:
                bot = ServerBot(server, config["RefreshRate"])
                asyncio.create_task(bot.start())
        
        # Start global bot if token is provided
        if config["GlobalBotDiscordToken"]:
            global_bot = GlobalBot(config)
            await global_bot.start()
        else:
            # If no global bot, just keep the program running
            while True:
                await asyncio.sleep(60)
    
    except Exception as e:
        print(f"Error in main: {e}")

if __name__ == "__main__":
    asyncio.run(main())